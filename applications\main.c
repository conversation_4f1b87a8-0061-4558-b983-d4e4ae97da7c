/*
 * Copyright (c) 2006-2023, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2015-07-29     Arda.Fu      first implementation
 */
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include <stdbool.h>
#include <n32g45x.h>
#include "main.h"
#include "lds_log.h"
#include "lds_ymodem.h"
#include "lds_flash.h"

#define MODEL_ID "AUX-EDUAudioProcessor-0001-boot"
#define VERSION_BUF "1.00.00"
 
void hw_us_delay(uint32_t us)
{
    if (us == 0) {
         return; // 延时0微秒直接返回
     }
       // 禁用SysTick，清除所有设置
    SysTick->CTRL = 0;
    // 设置SysTick重载值为最大值 (24位计数器)，使其自由运行
    SysTick->LOAD = 0xFFFFFF;
    // 清除当前计数器值
    SysTick->VAL = 0;
    // 配置SysTick使用HCLK作为时钟源，并使能计数器
    SysTick->CTRL = SysTick_CTRL_CLKSOURCE_Msk | SysTick_CTRL_ENABLE_Msk;
 
    // 计算每微秒对应的SysTick节拍数
    // 确保 SystemCoreClock 是正确的CPU频率（HCLK）
    uint32_t ticks_per_us = SystemCoreClock / 1000000UL; // 使用UL确保是无符号长整型除法
    if (ticks_per_us == 0) {
        // 如果SystemCoreClock太低或未初始化，ticks_per_us可能为0
        // 在这种情况下，无法进行精确延时，可以考虑报错或返回。
        // 这里简单地返回，避免除以0或无限循环。
        return;
    }
 
    // 计算总共需要等待的节拍数
    uint32_t total_ticks = us * ticks_per_us;
 
    // 获取延时开始时的SysTick计数器值
    uint32_t start_tick = SysTick->VAL;
    uint32_t elapsed_ticks = 0;
    uint32_t current_tick;
 
    // 忙等待循环
    while (elapsed_ticks < total_ticks) {
        current_tick = SysTick->VAL;
 
        // 判断是否发生回绕 (从 0xFFFFFF 变为 0)
        if (current_tick <= start_tick) {
            // 没有发生回绕，直接计算差值
            elapsed_ticks = start_tick - current_tick;
        } else {
            // 发生回绕，需要加上从 0xFFFFFF 到 0 这一段的节拍数
            // SysTick->LOAD + 1 是计数器的总范围 (例如 0xFFFFFF + 1)
            elapsed_ticks = (SysTick->LOAD + 1 - current_tick) + start_tick;
        }
    }
    SysTick->CTRL&=~SysTick_CTRL_ENABLE_Msk;       //close the count
    SysTick->VAL =0;       //clear timer value  
}
uint32_t ldsUtilReverseBits(uint32_t dword, size_t len)
{
    if (len == 0 || len > sizeof(dword))
    {
        printf("Invalid length for bit reversal: %zu bytes. Must be between 1 and %zu.", len, sizeof(dword));
        return 0;
    }

    switch (len)
    {
        case 1: // 8-bit reversal
            dword = ((dword >> 1) & 0x55) | ((dword & 0x55) << 1);
            dword = ((dword >> 2) & 0x33) | ((dword & 0x33) << 2);
            dword = ((dword >> 4) & 0x0F) | ((dword & 0x0F) << 4);
            return dword;

        case 2: // 16-bit reversal
            dword = ((dword >> 1) & 0x5555) | ((dword & 0x5555) << 1);
            dword = ((dword >> 2) & 0x3333) | ((dword & 0x3333) << 2);
            dword = ((dword >> 4) & 0x0F0F) | ((dword & 0x0F0F) << 4);
            dword = ((dword >> 8) & 0x00FF) | ((dword & 0x00FF) << 8);
            return dword;
        
        case 4: // 32-bit reversal
            dword = ((dword >> 1) & 0x55555555) | ((dword & 0x55555555) << 1);
            dword = ((dword >> 2) & 0x33333333) | ((dword & 0x33333333) << 2);
            dword = ((dword >> 4) & 0x0F0F0F0F) | ((dword & 0x0F0F0F0F) << 4);
            dword = ((dword >> 8) & 0x00FF00FF) | ((dword & 0x00FF00FF) << 8);
            dword = (dword >> 16) | (dword << 16);
            return dword;

        default: // Fallback for other cases (e.g., 3 bytes).
        {
            const size_t num_bits = len * 8;
            uint32_t reversed_dword = 0;
            for (size_t i = 0; i < num_bits; i++)
            {
                reversed_dword <<= 1;
                reversed_dword |= (dword & 1);
                dword >>= 1;
            }
            return reversed_dword;
        }
    }
}
static const crc32_table[] = {
    0x00000000, 0x77073096,  0xEE0E612C, 0x990951BA,   0x076DC419, 0x706AF48F,  0xE963A535, 0x9E6495A3,
    0x0EDB8832, 0x79DCB8A4,  0xE0D5E91E, 0x97D2D988,   0x09B64C2B, 0x7EB17CBD,  0xE7B82D07, 0x90BF1D91,
    0x1DB71064, 0x6AB020F2,  0xF3B97148, 0x84BE41DE,   0x1ADAD47D, 0x6DDDE4EB,  0xF4D4B551, 0x83D385C7,
    0x136C9856, 0x646BA8C0,  0xFD62F97A, 0x8A65C9EC,   0x14015C4F, 0x63066CD9,  0xFA0F3D63, 0x8D080DF5,
    0x3B6E20C8, 0x4C69105E,  0xD56041E4, 0xA2677172,   0x3C03E4D1, 0x4B04D447,  0xD20D85FD, 0xA50AB56B,
    0x35B5A8FA, 0x42B2986C,  0xDBBBC9D6, 0xACBCF940,   0x32D86CE3, 0x45DF5C75,  0xDCD60DCF, 0xABD13D59,
    0x26D930AC, 0x51DE003A,  0xC8D75180, 0xBFD06116,   0x21B4F4B5, 0x56B3C423,  0xCFBA9599, 0xB8BDA50F,
    0x2802B89E, 0x5F058808,  0xC60CD9B2, 0xB10BE924,   0x2F6F7C87, 0x58684C11,  0xC1611DAB, 0xB6662D3D,
    0x76DC4190, 0x01DB7106,  0x98D220BC, 0xEFD5102A,   0x71B18589, 0x06B6B51F,  0x9FBFE4A5, 0xE8B8D433,
    0x7807C9A2, 0x0F00F934,  0x9609A88E, 0xE10E9818,   0x7F6A0DBB, 0x086D3D2D,  0x91646C97, 0xE6635C01,
    0x6B6B51F4, 0x1C6C6162,  0x856530D8, 0xF262004E,   0x6C0695ED, 0x1B01A57B,  0x8208F4C1, 0xF50FC457,
    0x65B0D9C6, 0x12B7E950,  0x8BBEB8EA, 0xFCB9887C,   0x62DD1DDF, 0x15DA2D49,  0x8CD37CF3, 0xFBD44C65,
    0x4DB26158, 0x3AB551CE,  0xA3BC0074, 0xD4BB30E2,   0x4ADFA541, 0x3DD895D7,  0xA4D1C46D, 0xD3D6F4FB,
    0x4369E96A, 0x346ED9FC,  0xAD678846, 0xDA60B8D0,   0x44042D73, 0x33031DE5,  0xAA0A4C5F, 0xDD0D7CC9,
    0x5005713C, 0x270241AA,  0xBE0B1010, 0xC90C2086,   0x5768B525, 0x206F85B3,  0xB966D409, 0xCE61E49F,
    0x5EDEF90E, 0x29D9C998,  0xB0D09822, 0xC7D7A8B4,   0x59B33D17, 0x2EB40D81,  0xB7BD5C3B, 0xC0BA6CAD,
    0xEDB88320, 0x9ABFB3B6,  0x03B6E20C, 0x74B1D29A,   0xEAD54739, 0x9DD277AF,  0x04DB2615, 0x73DC1683,
    0xE3630B12, 0x94643B84,  0x0D6D6A3E, 0x7A6A5AA8,   0xE40ECF0B, 0x9309FF9D,  0x0A00AE27, 0x7D079EB1,
    0xF00F9344, 0x8708A3D2,  0x1E01F268, 0x6906C2FE,   0xF762575D, 0x806567CB,  0x196C3671, 0x6E6B06E7,
    0xFED41B76, 0x89D32BE0,  0x10DA7A5A, 0x67DD4ACC,   0xF9B9DF6F, 0x8EBEEFF9,  0x17B7BE43, 0x60B08ED5,
    0xD6D6A3E8, 0xA1D1937E,  0x38D8C2C4, 0x4FDFF252,   0xD1BB67F1, 0xA6BC5767,  0x3FB506DD, 0x48B2364B,
    0xD80D2BDA, 0xAF0A1B4C,  0x36034AF6, 0x41047A60,   0xDF60EFC3, 0xA867DF55,  0x316E8EEF, 0x4669BE79,
    0xCB61B38C, 0xBC66831A,  0x256FD2A0, 0x5268E236,   0xCC0C7795, 0xBB0B4703,  0x220216B9, 0x5505262F,
    0xC5BA3BBE, 0xB2BD0B28,  0x2BB45A92, 0x5CB36A04,   0xC2D7FFA7, 0xB5D0CF31,  0x2CD99E8B, 0x5BDEAE1D,
    0x9B64C2B0, 0xEC63F226,  0x756AA39C, 0x026D930A,   0x9C0906A9, 0xEB0E363F,  0x72076785, 0x05005713,
    0x95BF4A82, 0xE2B87A14,  0x7BB12BAE, 0x0CB61B38,   0x92D28E9B, 0xE5D5BE0D,  0x7CDCEFB7, 0x0BDBDF21,
    0x86D3D2D4, 0xF1D4E242,  0x68DDB3F8, 0x1FDA836E,   0x81BE16CD, 0xF6B9265B,  0x6FB077E1, 0x18B74777,
    0x88085AE6, 0xFF0F6A70,  0x66063BCA, 0x11010B5C,   0x8F659EFF, 0xF862AE69,  0x616BFFD3, 0x166CCF45,
    0xA00AE278, 0xD70DD2EE,  0x4E048354, 0x3903B3C2,   0xA7672661, 0xD06016F7,  0x4969474D, 0x3E6E77DB,
    0xAED16A4A, 0xD9D65ADC,  0x40DF0B66, 0x37D83BF0,   0xA9BCAE53, 0xDEBB9EC5,  0x47B2CF7F, 0x30B5FFE9,
    0xBDBDF21C, 0xCABAC28A,  0x53B39330, 0x24B4A3A6,   0xBAD03605, 0xCDD70693,  0x54DE5729, 0x23D967BF,
    0xB3667A2E, 0xC4614AB8,  0x5D681B02, 0x2A6F2B94,   0xB40BBE37, 0xC30C8EA1,  0x5A05DF1B, 0x2D02EF8D
};
//crc32-ISO-HDLC
uint32_t ldsUtilCheckCrc32(const uint8_t *data, uint32_t len)
{
   uint32_t crc = 0xFFFFFFFFUL; // 初始值
 
    for (size_t i = 0; i < len; i++) {
        // (crc ^ data[i]) & 0xFF 获取当前字节和CRC低8位异或后的结果作为查表的索引
        // crc >> 8 将CRC右移8位，为下一个字节腾出空间
        crc = (crc >> 8) ^ crc32_table[(crc ^ data[i]) & 0xFF];
    }
  
    return ~crc; // 最终结果取反
}

/**
 * @brief 校验APP区域的完整性
 * @param app_addr APP起始地址
 * @param app_size APP大小
 * @param expected_crc32 期望的CRC32值
 * @return true 校验成功, false 校验失败
 */
bool ldsVerifyAppIntegrity(uint32_t app_addr, uint32_t app_size, uint32_t expected_crc32)
{
    if (app_size == 0 || app_size % 4 != 0) {
        printf("Invalid app size: %u\n", app_size);
        return false;
    }
    
    uint32_t calculated_crc = ldsUtilCheckCrc32((const uint8_t*)app_addr, app_size);
    printf("App addr: 0x%08X, size: %u, expected CRC: 0x%08X, calculated CRC: 0x%08X\n", 
           app_addr, app_size, expected_crc32, calculated_crc);
    
    return (calculated_crc == expected_crc32);
}

/**
 * @brief 跳转到指定APP
 * @param app_addr APP起始地址
 */
void ldsJumpToApp(uint32_t app_addr)
{
    uint32_t stack_ptr = *((uint32_t*)app_addr);
    uint32_t reset_handler = *((uint32_t*)(app_addr + 4));
    
    printf("Jumping to APP at 0x%08X, stack: 0x%08X, reset: 0x%08X\n", 
           app_addr, stack_ptr, reset_handler);
    
    // 检查栈指针和复位向量的有效性
    if ((stack_ptr & 0xFFF00000) != 0x20000000 || 
        (reset_handler & 0xFFF00000) != 0x08000000) {
        printf("Invalid APP vectors\n");
        return;
    }
    
    // 禁用所有中断
    __disable_irq();
    
    // 设置栈指针
    __set_MSP(stack_ptr);
    
    // 跳转到APP
    ((void(*)(void))reset_handler)();
}

void delay_ms(uint32_t ms) 
{
    for (uint32_t i = 0; i < ms; i++) {
        hw_us_delay(1000); // 1毫秒 = 1000微秒
    }
}
 
int main(void)
{
    uint32_t count = 0;
    BootInfo_t* boot_info;
    bool app1_valid = false;
    bool app2_valid = false;
    
    /* Enable CRC clock */
    RCC_EnableAHBPeriphClk(RCC_AHB_PERIPH_CRC, ENABLE);

    ldsUartLogInit();
    ldsYmodemInit();

    printf("Starting %s version %s\n", MODEL_ID, VERSION_BUF);

    // 读取启动信息
    boot_info = (BootInfo_t*)BOOT_INFO_ADDR;
    
    // 校验boot info的完整性
    if (boot_info->magic == BOOT_INFO_MAGIC) {
        uint32_t boot_info_crc = ldsUtilCheckCrc32((const uint8_t*)boot_info, (sizeof(BootInfo_t) - 4));
        if (boot_info_crc == boot_info->crc32) {
            printf("Boot info valid, target: %s\n", 
                   (boot_info->boot_target == APP_TARGET_APP1) ? "APP1" : "APP2");
            
            // 校验APP1
            if (boot_info->app1_size > 0) {
                app1_valid = ldsVerifyAppIntegrity(APP1_START_ADDR, 
                                                  boot_info->app1_size, 
                                                  boot_info->app1_crc32);
                printf("APP1 verification: %s (version: %s)\n", 
                       app1_valid ? "PASS" : "FAIL", boot_info->app1_version);
            }
            
            // 校验APP2
            if (boot_info->app2_size > 0) {
                app2_valid = ldsVerifyAppIntegrity(APP2_START_ADDR, 
                                                  boot_info->app2_size, 
                                                  boot_info->app2_crc32);
                printf("APP2 verification: %s (version: %s)\n", 
                       app2_valid ? "PASS" : "FAIL", boot_info->app2_version);
            }
            
            // 根据启动目标和校验结果决定启动哪个APP
            if (boot_info->boot_target == APP_TARGET_APP1 && app1_valid) {
                printf("Booting APP1...\n");
                delay_ms(10);
                ldsJumpToApp(APP1_START_ADDR);
            } else if (boot_info->boot_target == APP_TARGET_APP2 && app2_valid) {
                printf("Booting APP2...\n");
                delay_ms(10);
                ldsJumpToApp(APP2_START_ADDR);
            } else if (app1_valid) {
                printf("Target APP invalid, fallback to APP1...\n");
                delay_ms(10);
                ldsJumpToApp(APP1_START_ADDR);
            } else if (app2_valid) {
                printf("Target APP invalid, fallback to APP2...\n");
                delay_ms(10);
                ldsJumpToApp(APP2_START_ADDR);
            }
        } else {
            printf("Boot info CRC error: expected 0x%08X, got 0x%08X\n", 
                   boot_info->crc32, boot_info_crc);
        }
    } else {
        printf("Boot info magic error: 0x%08X\n", boot_info->magic);
    }
    
    // 如果到达这里，说明两个APP区都校验失败，进入升级模式
    printf("Both APP regions invalid, entering YModem upgrade mode...\n");
    printf("Please send firmware file via YModem protocol\n");
    printf("Expected filename format: host-V1.00.02-23abcdef.bin\n");
    
    while (1) {
        lds_ymodem_result_t result = ldsYmodemUpgrade();
        
        if (result == YMODEM_OK) {
            printf("Upgrade completed successfully, rebooting...\n");
            delay_ms(1000);
            NVIC_SystemReset();
        } else {
            printf("Upgrade failed with error %d, retrying...\n", result);
            delay_ms(2000);
        }
    }
}
