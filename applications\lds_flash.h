/**
 * @file    lds_flash.h
 * @brief   Flash操作相关接口定义
 * <AUTHOR> @version v1.0.0
 * @date    2021-10-14
 * @par     Copyright
 * Copyright (c) <PERSON><PERSON><PERSON> Lighting 2018-2025. All rights reserved.
 *
 * @par     History
 * 1.Date         : 2021-10-14
 *   Modification : Create file
 */

/* prevent from redefinition ------------------------------------------------ */
#ifndef __APPLICATIONS_LDS_FLASH_H__
#define __APPLICATIONS_LDS_FLASH_H__

/* needed include files ----------------------------------------------------- */
#include <stdint.h>
#include <stdbool.h>
#include "n32g45x.h"
#include "system_n32g45x.h"

/* macro definition --------------------------------------------------------- */

#if 1
#define FLASH_BOOTLOADER_ADDR_START     0x08000000      /**< Bootloader起始地址，10KB */
#define FLASH_APP1_ADDR                 0x08002800      /**< 第一个应用程序起始地址(存放在FLASH), 220KB */
#define FLASH_APP1_ADDR_END             0x080397FF      /**< 第一个应用程序结束地址 */
#define FLASH_APP2_ADDR                 0x08039800      /**< 第二个应用程序起始地址(存放在FLASH), 220KB */
#define FLASH_APP2_ADDR_END             0x080767FF      /**< 第二个应用程序结束地址 */
#define FLASH_OTA_FLAG_ADDR             0x08076800      /**< OTA标志， 2KB */
#define FLASH_OTA_FLAG_ADDR_END         0x08076FFF      /**< OTA标志结束地址 */
#define FLASH_RESERVE_ADDR              0x08077000      /**< 保留20KB */
#define FLASH_RESERVE_ADDR_END          0x0807BFFF      /**< 保留区域结束地址 */
#define FLASH_FLASH_DB_ADDR             0x0807C000      /**< FlashDB 16K */

#define FLASH_APP_SIZE                  0x37000         /**< 应用程序大小 220KB */
#define FLASH_PAGE_SIZE                 2048            /**< Flash页大小 */
#else
#define FLASH_BOOTLOADER_ADDR_START     0x08000000      /**< Bootloader起始地址，60KB */
#define FLASH_APP1_ADDR                 0x0800F000      /**< 第一个应用程序起始地址(存放在FLASH), 170KB */
#define FLASH_APP1_ADDR_END             0x080397FF      /**< 第一个应用程序结束地址 */
#define FLASH_APP2_ADDR                 0x08039800      /**< 第二个应用程序起始地址(存放在FLASH), 220KB */
#define FLASH_APP2_ADDR_END             0x080767FF      /**< 第二个应用程序结束地址 */
#define FLASH_OTA_FLAG_ADDR             0x08076800      /**< OTA标志， 2KB */
#define FLASH_OTA_FLAG_ADDR_END         0x08076FFF      /**< OTA标志结束地址 */
#define FLASH_RESERVE_ADDR              0x08077000      /**< 保留20KB */
#define FLASH_RESERVE_ADDR_END          0x0807BFFF      /**< 保留区域结束地址 */
#define FLASH_FLASH_DB_ADDR             0x0807C000      /**< FlashDB 16K */

#define FLASH_APP_SIZE                  0x2A800         /**< 应用程序大小 170KB */
#define FLASH_PAGE_SIZE                 2048            /**< Flash页大小 */
#endif
#define BOOT_INFO_ADDR                  (FLASH_BASE + 0x80000 - 0x800)  /**< 启动信息地址 */
// APP分区定义
#define APP1_START_ADDR                 (FLASH_BASE + 0x8000)
#define APP2_START_ADDR                 (APP1_START_ADDR + 0x3C000) // (APP1_START_ADDR + 0x3BC00)
#define APP_MAX_SIZE                    (239 * 1024)  // 224KB

/* typedef ------------------------------------------------------------------ */


/* global variables declare ------------------------------------------------- */


/* global function declare -------------------------------------------------- */
/**
 * @brief Flash初始化
 *
 * @return 0: 成功, 其他: 失败
 */
int ldsFlashInit(void);

/**
 * @brief 向Flash写入数据
 *
 * @param[in] address 写入起始地址
 * @param[in] buf 写入数据缓冲区
 * @param[in] size 写入数据大小
 *
 * @return 0: 成功, 其他: 失败
 * @note 写入前需要先擦除Flash
 */
int ldsFlashWrite(uint32_t address, const uint8_t *buf, uint32_t size);

/**
 * @brief 从Flash读取数据
 *
 * @param[in] address 读取起始地址
 * @param[out] buf 读取数据缓冲区
 * @param[in] size 读取数据大小
 *
 * @return 0: 成功, 其他: 失败
 */
int ldsFlashRead(uint32_t address, uint8_t *buf, uint32_t size);

/**
 * @brief 擦除Flash指定地址和大小的数据
 *
 * @param[in] address 擦除起始地址
 * @param[in] size 擦除数据大小
 *
 * @return 0: 成功, 其他: 失败
 */
int ldsFlashErase(uint32_t address, uint32_t size);

/**
 * @brief 读取多个字(32位)数据
 *
 * @param[in] readAddr 读取起始地址
 * @param[out] pBuffer 读取数据缓冲区
 * @param[in] numToRead 读取字数量
 */
void ldsFlashReadNnumWord(uint32_t readAddr, uint32_t *pBuffer, uint16_t numToRead);

/**
 * @brief 读取多个字节数据
 *
 * @param[in] readAddr 读取起始地址
 * @param[out] pBuffer 读取数据缓冲区
 * @param[in] numToRead 读取字节数量
 *
 * @return Flash操作状态
 */
FLASH_STS ldsFlashReadNnumByte(uint32_t readAddr, uint8_t *pBuffer, uint16_t numToRead);

#endif /* __APPLICATIONS_LDS_FLASH_H__ */
/**************************** END OF FILE *************************************/
